import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { toast } from "sonner";

export function CampaignManager() {
  const [showForm, setShowForm] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    zealySubdomain: "",
    zealyCredentialId: "",
    twitterCredentialId: "",
    keywordSets: "",
    ambassadorUsernames: "",
    useTwitterCreatedAt: false,
  });

  const campaigns = useQuery(api.campaigns.listCampaigns) || [];
  const zealyCredentials = useQuery(api.credentials.listCredentials, { kind: "zealy" }) || [];
  const twitterCredentials = useQuery(api.credentials.listCredentials, { kind: "twitterapi" }) || [];
  const createCampaign = useMutation(api.campaigns.createCampaign);
  const updateCampaign = useMutation(api.campaigns.updateCampaign);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.zealySubdomain || !formData.zealyCredentialId) {
      toast.error("Please fill in required fields");
      return;
    }

    try {
      const keywordSets = formData.keywordSets
        .split('\n')
        .map(line => line.trim())
        .filter(line => line);

      const ambassadorUsernames = formData.ambassadorUsernames
        .split('\n')
        .map(line => line.trim().replace('@', ''))
        .filter(line => line);

      if (editingCampaign) {
        await updateCampaign({
          campaignId: editingCampaign as any,
          name: formData.name,
          twitterCredentialId: formData.twitterCredentialId ? formData.twitterCredentialId as any : undefined,
          keywordSets,
          ambassadorUsernames,
          useTwitterCreatedAt: formData.useTwitterCreatedAt,
        });
        toast.success("Campaign updated successfully");
      } else {
        await createCampaign({
          name: formData.name,
          zealySubdomain: formData.zealySubdomain,
          zealyCredentialId: formData.zealyCredentialId as any,
          twitterCredentialId: formData.twitterCredentialId ? formData.twitterCredentialId as any : undefined,
          keywordSets,
          ambassadorUsernames,
          useTwitterCreatedAt: formData.useTwitterCreatedAt,
        });
        toast.success("Campaign created successfully");
      }
      
      setFormData({
        name: "",
        zealySubdomain: "",
        zealyCredentialId: "",
        twitterCredentialId: "",
        keywordSets: "",
        ambassadorUsernames: "",
        useTwitterCreatedAt: false,
      });
      setShowForm(false);
      setEditingCampaign(null);
    } catch (error) {
      toast.error(`Failed to ${editingCampaign ? 'update' : 'create'} campaign`);
      console.error(error);
    }
  };

  const handleEdit = (campaign: any) => {
    setFormData({
      name: campaign.name,
      zealySubdomain: campaign.zealySubdomain,
      zealyCredentialId: campaign.zealyCredentialId,
      twitterCredentialId: campaign.twitterCredentialId || "",
      keywordSets: (campaign.keywordSets || []).join('\n'),
      ambassadorUsernames: (campaign.ambassadorUsernames || []).join('\n'),
      useTwitterCreatedAt: campaign.useTwitterCreatedAt || false,
    });
    setEditingCampaign(campaign._id);
    setShowForm(true);
  };

  const handleCancel = () => {
    setFormData({
      name: "",
      zealySubdomain: "",
      zealyCredentialId: "",
      twitterCredentialId: "",
      keywordSets: "",
      ambassadorUsernames: "",
      useTwitterCreatedAt: false,
    });
    setShowForm(false);
    setEditingCampaign(null);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Campaigns</h2>
          <p className="text-sm text-gray-600">
            Manage your Zealy community campaigns for tweet collection and Twitter integration
          </p>
        </div>
        <button
          onClick={() => setShowForm(!showForm)}
          disabled={zealyCredentials.length === 0}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {showForm ? "Cancel" : "New Campaign"}
        </button>
      </div>

      {zealyCredentials.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <p className="text-yellow-800">
            You need to add Zealy credentials before creating campaigns.
          </p>
        </div>
      )}

      {showForm && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium text-gray-900 mb-4">
            {editingCampaign ? "Edit Campaign" : "Create New Campaign"}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Campaign Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Q1 2025 Campaign"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Zealy Subdomain *
                </label>
                <input
                  type="text"
                  value={formData.zealySubdomain}
                  onChange={(e) => setFormData({ ...formData, zealySubdomain: e.target.value })}
                  placeholder="e.g., mycommunity"
                  disabled={!!editingCampaign}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Zealy API Credentials *
                </label>
                <select
                  value={formData.zealyCredentialId}
                  onChange={(e) => setFormData({ ...formData, zealyCredentialId: e.target.value })}
                  disabled={!!editingCampaign}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                >
                  <option value="">Select Zealy credentials...</option>
                  {zealyCredentials.map((cred) => (
                    <option key={cred._id} value={cred._id}>
                      {cred.label} ({cred.extra?.subdomain})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Twitter API Credentials (Optional)
                </label>
                <select
                  value={formData.twitterCredentialId}
                  onChange={(e) => setFormData({ ...formData, twitterCredentialId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">No Twitter integration</option>
                  {twitterCredentials.map((cred) => (
                    <option key={cred._id} value={cred._id}>
                      {cred.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {formData.twitterCredentialId && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Keyword Sets (one per line)
                  </label>
                  <textarea
                    value={formData.keywordSets}
                    onChange={(e) => setFormData({ ...formData, keywordSets: e.target.value })}
                    placeholder="#TRizer OR @trize_io&#10;#ForUTitan OR #ForUAI OR @4UAICrypto"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Each line is a separate keyword search. Use OR to combine terms.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ambassador Usernames (one per line)
                  </label>
                  <textarea
                    value={formData.ambassadorUsernames}
                    onChange={(e) => setFormData({ ...formData, ambassadorUsernames: e.target.value })}
                    placeholder="@ambassador1&#10;ambassador2&#10;@ambassador3"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Twitter usernames to collect tweets from. @ symbol is optional.
                  </p>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="useTwitterCreatedAt"
                    checked={formData.useTwitterCreatedAt}
                    onChange={(e) => setFormData({ ...formData, useTwitterCreatedAt: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="useTwitterCreatedAt" className="ml-2 block text-sm text-gray-900">
                    Use tweet's actual creation date for bucketing (instead of Zealy review date)
                  </label>
                </div>
              </>
            )}
            
            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                {editingCampaign ? "Update Campaign" : "Create Campaign"}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-3">
        {campaigns.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No campaigns created yet.</p>
            <p className="text-sm">Create your first campaign to start collecting tweets.</p>
          </div>
        ) : (
          campaigns.map((campaign) => (
            <div key={campaign._id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{campaign.name}</h3>
                  <p className="text-sm text-gray-600">
                    Subdomain: {campaign.zealySubdomain}
                  </p>
                  {campaign.twitterCredentialId && (
                    <div className="text-sm text-gray-600 mt-1">
                      <p>✓ Twitter integration enabled</p>
                      {campaign.keywordSets && campaign.keywordSets.length > 0 && (
                        <p>Keywords: {campaign.keywordSets.length} set(s)</p>
                      )}
                      {campaign.ambassadorUsernames && campaign.ambassadorUsernames.length > 0 && (
                        <p>Ambassadors: {campaign.ambassadorUsernames.length} user(s)</p>
                      )}
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-2">
                    Created {new Date(campaign._creationTime).toLocaleDateString()}
                    {campaign.lastRunAt && (
                      <span className="ml-2">
                        • Last run: {new Date(campaign.lastRunAt).toLocaleDateString()}
                      </span>
                    )}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleEdit(campaign)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Edit
                  </button>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    campaign.isActive 
                      ? "bg-green-100 text-green-800" 
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {campaign.isActive ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
