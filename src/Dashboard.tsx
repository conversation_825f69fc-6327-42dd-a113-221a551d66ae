import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { CredentialsManager } from "./CredentialsManager";
import { CampaignManager } from "./CampaignManager";
import { ImportRunner } from "./ImportRunner";
import { ExportManager } from "./ExportManager";

type Tab = "campaigns" | "credentials" | "import" | "export";

export function Dashboard() {
  const [activeTab, setActiveTab] = useState<Tab>("campaigns");
  const campaigns = useQuery(api.campaigns.listCampaigns) || [];

  const tabs = [
    { id: "campaigns" as const, label: "Campaigns", count: campaigns.length },
    { id: "credentials" as const, label: "API Keys" },
    { id: "import" as const, label: "Import Data" },
    { id: "export" as const, label: "Export" },
  ];

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.label}
              {tab.count !== undefined && (
                <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      <div className="bg-white rounded-lg shadow">
        {activeTab === "credentials" && <CredentialsManager />}
        {activeTab === "campaigns" && <CampaignManager />}
        {activeTab === "import" && <ImportRunner />}
        {activeTab === "export" && <ExportManager />}
      </div>
    </div>
  );
}
