import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { toast } from "sonner";

type CredentialType = "zealy" | "twitterapi";

export function CredentialsManager() {
  const [showForm, setShowForm] = useState(false);
  const [credentialType, setCredentialType] = useState<CredentialType>("zealy");
  const [formData, setFormData] = useState({
    label: "",
    apiKey: "",
    subdomain: "",
  });

  const credentials = useQuery(api.credentials.listCredentials, {}) || [];
  const saveZealyCredential = useMutation(api.credentials.saveZealyCredential);
  const saveTwitterCredential = useMutation(api.credentials.saveTwitterCredential);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.label || !formData.apiKey) {
      toast.error("Please fill in required fields");
      return;
    }

    if (credentialType === "zealy" && !formData.subdomain) {
      toast.error("Subdomain is required for Zealy credentials");
      return;
    }

    try {
      if (credentialType === "zealy") {
        await saveZealyCredential({
          label: formData.label,
          apiKey: formData.apiKey,
          subdomain: formData.subdomain,
        });
      } else {
        await saveTwitterCredential({
          label: formData.label,
          apiKey: formData.apiKey,
        });
      }
      
      toast.success(`${credentialType === "zealy" ? "Zealy" : "Twitter"} credentials saved successfully`);
      setFormData({ label: "", apiKey: "", subdomain: "" });
      setShowForm(false);
    } catch (error) {
      toast.error("Failed to save credentials");
      console.error(error);
    }
  };

  const zealyCredentials = credentials.filter(c => c.kind === "zealy");
  const twitterCredentials = credentials.filter(c => c.kind === "twitterapi");

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">API Credentials</h2>
          <p className="text-sm text-gray-600">
            Manage your Zealy and Twitter API keys for accessing data
          </p>
        </div>
        <button
          onClick={() => setShowForm(!showForm)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          {showForm ? "Cancel" : "Add Credentials"}
        </button>
      </div>

      {showForm && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Type
              </label>
              <select
                value={credentialType}
                onChange={(e) => setCredentialType(e.target.value as CredentialType)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="zealy">Zealy API</option>
                <option value="twitterapi">Twitter API (twitterapi.io)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Label
              </label>
              <input
                type="text"
                value={formData.label}
                onChange={(e) => setFormData({ ...formData, label: e.target.value })}
                placeholder="e.g., Main Community, Production Twitter"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            {credentialType === "zealy" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Zealy Subdomain
                </label>
                <input
                  type="text"
                  value={formData.subdomain}
                  onChange={(e) => setFormData({ ...formData, subdomain: e.target.value })}
                  placeholder="e.g., mycommunity"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  The subdomain from your Zealy community URL
                </p>
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key
              </label>
              <input
                type="password"
                value={formData.apiKey}
                onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
                placeholder={`Your ${credentialType === "zealy" ? "Zealy" : "Twitter"} API key`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {credentialType === "twitterapi" && (
                <p className="text-xs text-gray-500 mt-1">
                  Get your API key from <a href="https://twitterapi.io" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">twitterapi.io</a>
                </p>
              )}
            </div>
            
            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Save Credentials
              </button>
              <button
                type="button"
                onClick={() => setShowForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="space-y-6">
        {/* Zealy Credentials */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-3">Zealy API Credentials</h3>
          {zealyCredentials.length === 0 ? (
            <div className="text-center py-6 text-gray-500 border border-gray-200 rounded-lg">
              <p>No Zealy credentials configured yet.</p>
              <p className="text-sm">Add your first Zealy API key to get started.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {zealyCredentials.map((cred) => (
                <div key={cred._id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900">{cred.label}</h4>
                      <p className="text-sm text-gray-600">
                        Subdomain: {cred.extra?.subdomain}
                      </p>
                      <p className="text-xs text-gray-500">
                        Added {new Date(cred._creationTime).toLocaleDateString()}
                      </p>
                    </div>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      Active
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Twitter Credentials */}
        <div>
          <h3 className="text-md font-medium text-gray-900 mb-3">Twitter API Credentials</h3>
          {twitterCredentials.length === 0 ? (
            <div className="text-center py-6 text-gray-500 border border-gray-200 rounded-lg">
              <p>No Twitter credentials configured yet.</p>
              <p className="text-sm">Add a Twitter API key to enable enrichment and discovery features.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {twitterCredentials.map((cred) => (
                <div key={cred._id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-gray-900">{cred.label}</h4>
                      <p className="text-sm text-gray-600">
                        Twitter API (twitterapi.io)
                      </p>
                      <p className="text-xs text-gray-500">
                        Added {new Date(cred._creationTime).toLocaleDateString()}
                      </p>
                    </div>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      Active
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
