import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { toast } from "sonner";

export function ExportManager() {
  const [selectedCampaign, setSelectedCampaign] = useState("");
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });
  const [bucketType, setBucketType] = useState<"weekly" | "custom">("weekly");
  const [isExporting, setIsExporting] = useState(false);

  const campaigns = useQuery(api.campaigns.listCampaigns) || [];
  const exportData = useQuery(
    api.exports.getExportData,
    selectedCampaign && dateRange.from && dateRange.to
      ? {
          campaignId: selectedCampaign as any,
          fromDate: new Date(dateRange.from).getTime(),
          toDate: new Date(dateRange.to).getTime(),
          bucketType,
        }
      : "skip"
  );

  const handleExport = async (format: "csv" | "xlsx") => {
    if (!selectedCampaign || !dateRange.from || !dateRange.to) {
      toast.error("Please select campaign and date range");
      return;
    }

    if (!exportData) {
      toast.error("No data to export");
      return;
    }

    setIsExporting(true);

    try {
      const params = new URLSearchParams({
        campaignId: selectedCampaign,
        fromDate: new Date(dateRange.from).getTime().toString(),
        toDate: new Date(dateRange.to).getTime().toString(),
        format,
        bucketType,
      });

      // In a real app, this would be an API route that generates the file
      // For now, we'll create a simple CSV download
      if (format === "csv") {
        const csvContent = generateCSV(exportData.rows);
        downloadFile(csvContent, `zealy-export-${Date.now()}.csv`, "text/csv");
      } else {
        toast.info("Excel export would be implemented with ExcelJS in the API route");
      }

      toast.success(`${format.toUpperCase()} export completed`);
    } catch (error) {
      toast.error("Export failed: " + (error as Error).message);
      console.error(error);
    } finally {
      setIsExporting(false);
    }
  };

  const generateCSV = (rows: any[]) => {
    if (rows.length === 0) return "";
    
    const headers = Object.keys(rows[0]);
    const csvRows = [
      headers.join(","),
      ...rows.map(row => 
        headers.map(header => {
          const value = row[header];
          // Escape commas and quotes
          if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value;
        }).join(",")
      )
    ];
    
    return csvRows.join("\n");
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Export Data</h2>
        <p className="text-sm text-gray-600">
          Export collected tweet data in CSV or Excel format with weekly or custom bucketing
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Export Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Campaign
            </label>
            <select
              value={selectedCampaign}
              onChange={(e) => setSelectedCampaign(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select campaign...</option>
              {campaigns.map((campaign) => (
                <option key={campaign._id} value={campaign._id}>
                  {campaign.name} ({campaign.zealySubdomain})
                </option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bucketing
            </label>
            <select
              value={bucketType}
              onChange={(e) => setBucketType(e.target.value as "weekly" | "custom")}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="weekly">Weekly (ISO Mon-Sun)</option>
              <option value="custom">Custom Ranges</option>
            </select>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => handleExport("csv")}
              disabled={!selectedCampaign || !dateRange.from || !dateRange.to || isExporting}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isExporting ? "Exporting..." : "Export CSV"}
            </button>
            
            <button
              onClick={() => handleExport("xlsx")}
              disabled={!selectedCampaign || !dateRange.from || !dateRange.to || isExporting}
              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isExporting ? "Exporting..." : "Export Excel"}
            </button>
          </div>
        </div>

        {/* Preview */}
        <div>
          <h3 className="font-medium text-gray-900 mb-4">Export Preview</h3>
          
          {exportData ? (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Total Tweets:</span>
                  <span className="ml-2">{exportData.summary.totalTweets}</span>
                </div>
                <div>
                  <span className="font-medium">Unique Tweet IDs:</span>
                  <span className="ml-2">{exportData.summary.uniqueTweetIds}</span>
                </div>
                <div>
                  <span className="font-medium">Buckets:</span>
                  <span className="ml-2">{exportData.summary.buckets}</span>
                </div>
                <div>
                  <span className="font-medium">Date Range:</span>
                  <span className="ml-2 text-xs">
                    {new Date(exportData.summary.dateRange.from).toLocaleDateString()} - 
                    {new Date(exportData.summary.dateRange.to).toLocaleDateString()}
                  </span>
                </div>
              </div>
              
              {exportData.rows.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Sample Rows:</h4>
                  <div className="bg-white p-2 rounded border text-xs overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-1">Quest</th>
                          <th className="text-left p-1">User</th>
                          <th className="text-left p-1">Tweet ID</th>
                          <th className="text-left p-1">Source</th>
                        </tr>
                      </thead>
                      <tbody>
                        {exportData.rows.slice(0, 3).map((row, i) => (
                          <tr key={i} className="border-b">
                            <td className="p-1 truncate max-w-20">{row.quest_name}</td>
                            <td className="p-1 truncate max-w-20">{row.zealy_user_name}</td>
                            <td className="p-1">{row.tweet_id}</td>
                            <td className="p-1">{row.source}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {exportData.rows.length > 3 && (
                      <p className="text-center text-gray-500 mt-2">
                        ... and {exportData.rows.length - 3} more rows
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Select campaign and date range to preview export data</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
