import { useState } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "../convex/_generated/api";
import { toast } from "sonner";

export function ImportRunner() {
  const [selectedCampaign, setSelectedCampaign] = useState("");
  const [dateRange, setDateRange] = useState({
    from: "",
    to: "",
  });
  const [isRunning, setIsRunning] = useState<string | null>(null);

  const campaigns = useQuery(api.campaigns.listCampaigns) || [];
  const importRuns = useQuery(
    api.exports.getImportRuns,
    selectedCampaign ? { campaignId: selectedCampaign as any } : "skip"
  ) || [];
  
  const runZealyImport = useAction(api.zealy.runZealyImport);
  const runTwitterKeywordSearch = useAction(api.twitter.runTwitterKeywordSearch);
  const runTwitterAmbassadorSearch = useAction(api.twitter.runTwitterAmbassadorSearch);
  const enrichZealyTweets = useAction(api.twitter.enrichZealyTweets);

  const selectedCampaignData = campaigns.find(c => c._id === selectedCampaign);

  const handleRunImport = async (importType: "zealy" | "twitter_keywords" | "twitter_ambassadors" | "twitter_enrichment") => {
    if (!selectedCampaign) {
      toast.error("Please select a campaign");
      return;
    }

    if (importType !== "twitter_enrichment" && importType !== "zealy" && !selectedCampaignData?.twitterCredentialId) {
      toast.error("Twitter API credentials required for this import type");
      return;
    }

    if (importType === "twitter_keywords" && (!selectedCampaignData?.keywordSets || selectedCampaignData.keywordSets.length === 0)) {
      toast.error("No keywords configured for this campaign");
      return;
    }

    if (importType === "twitter_ambassadors" && (!selectedCampaignData?.ambassadorUsernames || selectedCampaignData.ambassadorUsernames.length === 0)) {
      toast.error("No ambassador usernames configured for this campaign");
      return;
    }

    setIsRunning(importType);
    
    try {
      const fromDate = dateRange.from ? new Date(dateRange.from).getTime() : undefined;
      const toDate = dateRange.to ? new Date(dateRange.to).getTime() : undefined;

      let result;
      switch (importType) {
        case "zealy":
          result = await runZealyImport({
            campaignId: selectedCampaign as any,
            fromDate,
            toDate,
          });
          toast.success(
            `Zealy import completed! Scanned ${result.questsScanned} quests, ${result.reviewsScanned} reviews, extracted ${result.tweetsExtracted} tweets.`
          );
          break;
        case "twitter_keywords":
          result = await runTwitterKeywordSearch({
            campaignId: selectedCampaign as any,
            fromDate,
            toDate,
          });
          toast.success(
            `Keyword search completed! Found ${result.tweetsExtracted} tweets.`
          );
          break;
        case "twitter_ambassadors":
          result = await runTwitterAmbassadorSearch({
            campaignId: selectedCampaign as any,
            fromDate,
            toDate,
          });
          toast.success(
            `Ambassador search completed! Found ${result.tweetsExtracted} tweets.`
          );
          break;
        case "twitter_enrichment":
          result = await enrichZealyTweets({
            campaignId: selectedCampaign as any,
          });
          toast.success(
            `Tweet enrichment completed! Enriched ${result.tweetsEnriched} tweets.`
          );
          break;
      }
    } catch (error) {
      toast.error(`Import failed: ${(error as Error).message}`);
      console.error(error);
    } finally {
      setIsRunning(null);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Import Data</h2>
        <p className="text-sm text-gray-600">
          Run imports to collect data from Zealy and Twitter APIs
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Import Form */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium text-gray-900 mb-4">Run New Import</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Campaign
              </label>
              <select
                value={selectedCampaign}
                onChange={(e) => setSelectedCampaign(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select campaign...</option>
                {campaigns.map((campaign) => (
                  <option key={campaign._id} value={campaign._id}>
                    {campaign.name} ({campaign.zealySubdomain})
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  From Date (Optional)
                </label>
                <input
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  To Date (Optional)
                </label>
                <input
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="space-y-2">
              <button
                onClick={() => handleRunImport("zealy")}
                disabled={!selectedCampaign || isRunning === "zealy"}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isRunning === "zealy" ? "Running Zealy Import..." : "Import from Zealy"}
              </button>

              {selectedCampaignData?.twitterCredentialId && (
                <>
                  <button
                    onClick={() => handleRunImport("twitter_keywords")}
                    disabled={!selectedCampaign || isRunning === "twitter_keywords" || !selectedCampaignData?.keywordSets?.length}
                    className="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isRunning === "twitter_keywords" ? "Running Keyword Search..." : "Search by Keywords"}
                  </button>

                  <button
                    onClick={() => handleRunImport("twitter_ambassadors")}
                    disabled={!selectedCampaign || isRunning === "twitter_ambassadors" || !selectedCampaignData?.ambassadorUsernames?.length}
                    className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isRunning === "twitter_ambassadors" ? "Running Ambassador Search..." : "Search Ambassador Timelines"}
                  </button>

                  <button
                    onClick={() => handleRunImport("twitter_enrichment")}
                    disabled={!selectedCampaign || isRunning === "twitter_enrichment"}
                    className="w-full bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isRunning === "twitter_enrichment" ? "Enriching Tweets..." : "Enrich Zealy Tweets"}
                  </button>
                </>
              )}
            </div>

            {selectedCampaignData && !selectedCampaignData.twitterCredentialId && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <p className="text-yellow-800 text-sm">
                  Add Twitter API credentials to this campaign to enable keyword search, ambassador timelines, and tweet enrichment.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Runs */}
        <div>
          <h3 className="font-medium text-gray-900 mb-4">Recent Import Runs</h3>
          
          {importRuns.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No import runs yet.</p>
              <p className="text-sm">Select a campaign and run your first import.</p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {importRuns.map((run) => (
                <div key={run._id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        run.status === "completed" 
                          ? "bg-green-100 text-green-800"
                          : run.status === "failed"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}>
                        {run.status}
                      </span>
                      <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
                        {run.runType.replace('_', ' ')}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(run.startedAt).toLocaleString()}
                    </span>
                  </div>
                  
                  {run.status === "completed" && (
                    <div className="text-sm text-gray-600">
                      {run.runType === "zealy" && (
                        <>
                          <p>Quests: {run.questsScanned}</p>
                          <p>Reviews: {run.reviewsScanned}</p>
                        </>
                      )}
                      <p>Tweets: {run.tweetsExtracted}</p>
                      {(run.tweetsEnriched ?? 0) > 0 && (
                        <p>Enriched: {run.tweetsEnriched}</p>
                      )}
                    </div>
                  )}
                  
                  {run.status === "failed" && run.errorMessage && (
                    <p className="text-sm text-red-600 mt-1">
                      Error: {run.errorMessage}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
