"use node";

import { action, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Twitter API types
interface TwitterUser {
  id: string;
  name: string;
  username: string;
}

interface TwitterTweet {
  id: string;
  text: string;
  created_at: string;
  author: TwitterUser;
  public_metrics: {
    like_count: number;
    reply_count: number;
    retweet_count: number;
    quote_count: number;
    impression_count?: number;
  };
}

interface TwitterSearchResponse {
  tweets: TwitterTweet[];
  next_cursor?: string;
}

interface TwitterUserTimelineResponse {
  tweets: TwitterTweet[];
  next_cursor?: string;
}

interface TwitterTweetsByIdsResponse {
  tweets: TwitterTweet[];
}

// Helper to make Twitter API calls with rate limiting
async function twitterApiCall(
  endpoint: string,
  apiKey: string,
  params: Record<string, string> = {}
): Promise<any> {
  const url = new URL(`https://api.twitterapi.io${endpoint}`);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) url.searchParams.append(key, value);
  });

  const response = await fetch(url.toString(), {
    headers: {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    if (response.status === 429) {
      // Rate limited - wait and retry
      await new Promise(resolve => setTimeout(resolve, 2000));
      return twitterApiCall(endpoint, apiKey, params);
    }
    throw new Error(`Twitter API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Build Twitter search query with date constraints
function buildSearchQuery(keywords: string[], fromDate?: number, toDate?: number): string {
  let query = keywords.join(" OR ");
  
  if (fromDate) {
    const fromDateStr = new Date(fromDate).toISOString().split('T')[0].replace(/-/g, '_') + '_00:00:00_UTC';
    query += ` since:${fromDateStr}`;
  }
  
  if (toDate) {
    const toDateStr = new Date(toDate).toISOString().split('T')[0].replace(/-/g, '_') + '_23:59:59_UTC';
    query += ` until:${toDateStr}`;
  }
  
  return query;
}

export const runTwitterKeywordSearch = action({
  args: {
    campaignId: v.id("campaigns"),
    fromDate: v.optional(v.number()),
    toDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Start import run
    const runId = await ctx.runMutation(internal.database.startImportRun, {
      campaignId: args.campaignId,
      runType: "twitter_keywords",
    });

    try {
      // Get campaign and credentials
      const campaign = await ctx.runQuery(internal.zealy.getCampaignWithCredentials, {
        campaignId: args.campaignId,
      });

      if (!campaign || !campaign.twitterApiKey) {
        throw new Error("Campaign or Twitter API key not found");
      }

      if (!campaign.keywordSets || campaign.keywordSets.length === 0) {
        throw new Error("No keywords configured for this campaign");
      }

      let tweetsExtracted = 0;

      // Search for each keyword set
      for (const keywordSet of campaign.keywordSets) {
        const keywords = keywordSet.split(',').map(k => k.trim()).filter(k => k);
        if (keywords.length === 0) continue;

        const query = buildSearchQuery(keywords, args.fromDate, args.toDate);
        let cursor: string | undefined;

        do {
          const params: Record<string, string> = {
            query,
            queryType: "Latest",
          };
          if (cursor) params.cursor = cursor;

          const searchResponse: TwitterSearchResponse = await twitterApiCall(
            "/twitter/tweet/advanced_search",
            campaign.twitterApiKey,
            params
          );

          for (const tweet of searchResponse.tweets) {
            // Store extracted tweet
            await ctx.runMutation(internal.zealy.upsertExtractedTweet, {
              campaignId: args.campaignId,
              tweetId: tweet.id,
              tweetUrl: `https://x.com/i/status/${tweet.id}`,
              reactTarget: false,
              source: "keyword_search",
            });

            // Store enrichment data
            await ctx.runMutation(internal.tweetEnrichment.upsertTweetEnrichment, {
              tweetId: tweet.id,
              authorUsername: tweet.author.username,
              authorName: tweet.author.name,
              tweetText: tweet.text,
              tweetCreatedAt: new Date(tweet.created_at).getTime(),
              likeCount: tweet.public_metrics.like_count,
              replyCount: tweet.public_metrics.reply_count,
              retweetCount: tweet.public_metrics.retweet_count,
              quoteCount: tweet.public_metrics.quote_count,
              viewCount: tweet.public_metrics.impression_count || 0,
            });

            tweetsExtracted++;
          }

          cursor = searchResponse.next_cursor;
        } while (cursor);
      }

      // Complete import run
      await ctx.runMutation(internal.zealy.completeImportRun, {
        runId,
        questsScanned: 0,
        reviewsScanned: 0,
        tweetsExtracted,
        tweetsEnriched: tweetsExtracted,
      });

      return {
        success: true,
        tweetsExtracted,
      };

    } catch (error) {
      await ctx.runMutation(internal.zealy.failImportRun, {
        runId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  },
});

export const runTwitterAmbassadorSearch = action({
  args: {
    campaignId: v.id("campaigns"),
    fromDate: v.optional(v.number()),
    toDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Start import run
    const runId = await ctx.runMutation(internal.database.startImportRun, {
      campaignId: args.campaignId,
      runType: "twitter_ambassadors",
    });

    try {
      // Get campaign and credentials
      const campaign = await ctx.runQuery(internal.zealy.getCampaignWithCredentials, {
        campaignId: args.campaignId,
      });

      if (!campaign || !campaign.twitterApiKey) {
        throw new Error("Campaign or Twitter API key not found");
      }

      if (!campaign.ambassadorUsernames || campaign.ambassadorUsernames.length === 0) {
        throw new Error("No ambassador usernames configured for this campaign");
      }

      let tweetsExtracted = 0;

      // Fetch tweets for each ambassador
      for (const username of campaign.ambassadorUsernames) {
        let cursor: string | undefined;

        do {
          const params: Record<string, string> = {
            userName: username,
            includeReplies: "false",
          };
          if (cursor) params.cursor = cursor;

          const timelineResponse: TwitterUserTimelineResponse = await twitterApiCall(
            "/twitter/user/last_tweets",
            campaign.twitterApiKey,
            params
          );

          let shouldContinue = true;

          for (const tweet of timelineResponse.tweets) {
            const tweetDate = new Date(tweet.created_at).getTime();
            
            // Stop if tweet is before our date range
            if (args.fromDate && tweetDate < args.fromDate) {
              shouldContinue = false;
              break;
            }
            
            // Skip if tweet is after our date range
            if (args.toDate && tweetDate > args.toDate) {
              continue;
            }

            // Store extracted tweet
            await ctx.runMutation(internal.zealy.upsertExtractedTweet, {
              campaignId: args.campaignId,
              tweetId: tweet.id,
              tweetUrl: `https://x.com/i/status/${tweet.id}`,
              reactTarget: false,
              source: "ambassador_timeline",
            });

            // Store enrichment data
            await ctx.runMutation(internal.tweetEnrichment.upsertTweetEnrichment, {
              tweetId: tweet.id,
              authorUsername: tweet.author.username,
              authorName: tweet.author.name,
              tweetText: tweet.text,
              tweetCreatedAt: tweetDate,
              likeCount: tweet.public_metrics.like_count,
              replyCount: tweet.public_metrics.reply_count,
              retweetCount: tweet.public_metrics.retweet_count,
              quoteCount: tweet.public_metrics.quote_count,
              viewCount: tweet.public_metrics.impression_count || 0,
            });

            tweetsExtracted++;
          }

          cursor = shouldContinue ? timelineResponse.next_cursor : undefined;
        } while (cursor);
      }

      // Complete import run
      await ctx.runMutation(internal.zealy.completeImportRun, {
        runId,
        questsScanned: 0,
        reviewsScanned: 0,
        tweetsExtracted,
        tweetsEnriched: tweetsExtracted,
      });

      return {
        success: true,
        tweetsExtracted,
      };

    } catch (error) {
      await ctx.runMutation(internal.zealy.failImportRun, {
        runId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  },
});

export const enrichZealyTweets = action({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    // Start import run
    const runId = await ctx.runMutation(internal.zealy.startImportRun, {
      campaignId: args.campaignId,
      runType: "twitter_enrichment",
    });

    try {
      // Get campaign and credentials
      const campaign = await ctx.runQuery(internal.zealy.getCampaignWithCredentials, {
        campaignId: args.campaignId,
      });

      if (!campaign || !campaign.twitterApiKey) {
        throw new Error("Campaign or Twitter API key not found");
      }

      // Get unenriched tweets from Zealy
      const unenrichedTweets = await ctx.runQuery(internal.tweetEnrichment.getUnenrichedTweets, {
        campaignId: args.campaignId,
      });

      let tweetsEnriched = 0;

      // Process tweets in batches of 100 (API limit)
      const batchSize = 100;
      for (let i = 0; i < unenrichedTweets.length; i += batchSize) {
        const batch = unenrichedTweets.slice(i, i + batchSize);
        const tweetIds = batch.map((t: any) => t.tweetId).join(',');

        const tweetsResponse: TwitterTweetsByIdsResponse = await twitterApiCall(
          "/twitter/tweets",
          campaign.twitterApiKey,
          { tweet_ids: tweetIds }
        );

        for (const tweet of tweetsResponse.tweets) {
          // Store enrichment data
          await ctx.runMutation(internal.tweetEnrichment.upsertTweetEnrichment, {
            tweetId: tweet.id,
            authorUsername: tweet.author.username,
            authorName: tweet.author.name,
            tweetText: tweet.text,
            tweetCreatedAt: new Date(tweet.created_at).getTime(),
            likeCount: tweet.public_metrics.like_count,
            replyCount: tweet.public_metrics.reply_count,
            retweetCount: tweet.public_metrics.retweet_count,
            quoteCount: tweet.public_metrics.quote_count,
            viewCount: tweet.public_metrics.impression_count || 0,
          });

          tweetsEnriched++;
        }
      }

      // Complete import run
      await ctx.runMutation(internal.zealy.completeImportRun, {
        runId,
        questsScanned: 0,
        reviewsScanned: 0,
        tweetsExtracted: 0,
        tweetsEnriched,
      });

      return {
        success: true,
        tweetsEnriched,
      };

    } catch (error) {
      await ctx.runMutation(internal.zealy.failImportRun, {
        runId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  },
});





