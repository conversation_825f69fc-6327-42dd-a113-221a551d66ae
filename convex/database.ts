import { internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";

// Import run management
export const startImportRun = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    runType: v.union(v.literal("zealy"), v.literal("twitter_keywords"), v.literal("twitter_ambassadors"), v.literal("twitter_enrichment")),
  },
  handler: async (ctx, args) => {
    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign) {
      throw new Error("Campaign not found");
    }

    return await ctx.db.insert("importRuns", {
      campaignId: args.campaignId,
      userId: campaign.userId,
      startedAt: Date.now(),
      status: "running",
      runType: args.runType,
      questsScanned: 0,
      reviewsScanned: 0,
      tweetsExtracted: 0,
      tweetsEnriched: 0,
    });
  },
});

export const completeImportRun = internalMutation({
  args: {
    runId: v.id("importRuns"),
    questsScanned: v.number(),
    reviewsScanned: v.number(),
    tweetsExtracted: v.number(),
    tweetsEnriched: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.runId, {
      completedAt: Date.now(),
      status: "completed",
      questsScanned: args.questsScanned,
      reviewsScanned: args.reviewsScanned,
      tweetsExtracted: args.tweetsExtracted,
      tweetsEnriched: args.tweetsEnriched,
    });

    // Update campaign last run time
    const run = await ctx.db.get(args.runId);
    if (run) {
      await ctx.db.patch(run.campaignId, {
        lastRunAt: Date.now(),
      });
    }
  },
});

export const failImportRun = internalMutation({
  args: {
    runId: v.id("importRuns"),
    errorMessage: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.runId, {
      completedAt: Date.now(),
      status: "failed",
      errorMessage: args.errorMessage,
    });
  },
});

export const getCampaignWithCredentials = internalQuery({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign) return null;

    const zealyCredential = await ctx.db.get(campaign.zealyCredentialId);
    if (!zealyCredential) return null;

    // Decrypt API key (simplified - use proper decryption in production)
    const zealyApiKey = Buffer.from(zealyCredential.secretEnc, 'base64').toString();

    let twitterApiKey: string | undefined;
    if (campaign.twitterCredentialId) {
      const twitterCredential = await ctx.db.get(campaign.twitterCredentialId);
      if (twitterCredential) {
        twitterApiKey = Buffer.from(twitterCredential.secretEnc, 'base64').toString();
      }
    }

    return {
      ...campaign,
      zealyApiKey,
      twitterApiKey,
    };
  },
});

// Quest management
export const upsertQuest = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    zealyQuestId: v.string(),
    name: v.string(),
    archived: v.boolean(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("quests")
      .withIndex("by_zealy_quest_id", (q) => q.eq("zealyQuestId", args.zealyQuestId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        name: args.name,
        archived: args.archived,
        lastUpdated: Date.now(),
      });
      return existing._id;
    } else {
      return await ctx.db.insert("quests", {
        campaignId: args.campaignId,
        zealyQuestId: args.zealyQuestId,
        name: args.name,
        archived: args.archived,
        lastUpdated: Date.now(),
      });
    }
  },
});

// Review management
export const upsertReview = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    zealyReviewId: v.string(),
    zealyUserId: v.string(),
    zealyUserName: v.string(),
    zealyQuestId: v.string(),
    zealyQuestName: v.string(),
    status: v.union(v.literal("pending"), v.literal("success"), v.literal("fail")),
    createdAt: v.number(),
    updatedAt: v.number(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("reviews")
      .withIndex("by_zealy_review_id", (q) => q.eq("zealyReviewId", args.zealyReviewId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        zealyUserName: args.zealyUserName,
        zealyQuestName: args.zealyQuestName,
        status: args.status,
        updatedAt: args.updatedAt,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("reviews", args);
    }
  },
});

export const upsertReviewTask = internalMutation({
  args: {
    reviewId: v.id("reviews"),
    taskId: v.string(),
    taskType: v.string(),
    taskCreatedAt: v.number(),
    rawValueJson: v.string(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("reviewTasks")
      .withIndex("by_review", (q) => q.eq("reviewId", args.reviewId))
      .filter((q) => q.eq(q.field("taskId"), args.taskId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        taskType: args.taskType,
        taskCreatedAt: args.taskCreatedAt,
        rawValueJson: args.rawValueJson,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("reviewTasks", args);
    }
  },
});

export const upsertExtractedTweet = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    reviewId: v.optional(v.id("reviews")),
    taskId: v.optional(v.string()),
    tweetId: v.string(),
    tweetUrl: v.string(),
    reactTarget: v.boolean(),
    source: v.union(
      v.literal("tweet"), 
      v.literal("tweetReact"), 
      v.literal("url"), 
      v.literal("text"),
      v.literal("keyword_search"),
      v.literal("ambassador_timeline")
    ),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("tweetsExtracted")
      .withIndex("by_tweet_and_review", (q) => 
        q.eq("tweetId", args.tweetId).eq("reviewId", args.reviewId ?? undefined)
      )
      .first();

    if (!existing) {
      await ctx.db.insert("tweetsExtracted", {
        ...args,
        extractedAt: Date.now(),
      });
    }
  },
});
