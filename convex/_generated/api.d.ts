/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as campaigns from "../campaigns.js";
import type * as credentials from "../credentials.js";
import type * as database from "../database.js";
import type * as exports from "../exports.js";
import type * as http from "../http.js";
import type * as router from "../router.js";
import type * as tweetEnrichment from "../tweetEnrichment.js";
import type * as twitter from "../twitter.js";
import type * as zealy from "../zealy.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  campaigns: typeof campaigns;
  credentials: typeof credentials;
  database: typeof database;
  exports: typeof exports;
  http: typeof http;
  router: typeof router;
  tweetEnrichment: typeof tweetEnrichment;
  twitter: typeof twitter;
  zealy: typeof zealy;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
