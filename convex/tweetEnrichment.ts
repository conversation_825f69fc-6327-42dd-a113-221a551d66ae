import { internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";

export const getUnenrichedTweets = internalQuery({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("tweetsExtracted")
      .withIndex("by_campaign", (q) => q.eq("campaignId", args.campaignId))
      .filter((q) => q.eq(q.field("enrichedAt"), undefined))
      .filter((q) => q.neq(q.field("source"), "keyword_search"))
      .filter((q) => q.neq(q.field("source"), "ambassador_timeline"))
      .collect();
  },
});

export const upsertTweetEnrichment = internalMutation({
  args: {
    tweetId: v.string(),
    authorUsername: v.string(),
    authorName: v.string(),
    tweetText: v.string(),
    tweetCreatedAt: v.number(),
    likeCount: v.number(),
    replyCount: v.number(),
    retweetCount: v.number(),
    quoteCount: v.number(),
    viewCount: v.number(),
  },
  handler: async (ctx, args) => {
    const tweets = await ctx.db
      .query("tweetsExtracted")
      .withIndex("by_tweet_id", (q) => q.eq("tweetId", args.tweetId))
      .collect();

    for (const tweet of tweets) {
      await ctx.db.patch(tweet._id, {
        authorUsername: args.authorUsername,
        authorName: args.authorName,
        tweetText: args.tweetText,
        tweetCreatedAt: args.tweetCreatedAt,
        likeCount: args.likeCount,
        replyCount: args.replyCount,
        retweetCount: args.retweetCount,
        quoteCount: args.quoteCount,
        viewCount: args.viewCount,
        enrichedAt: Date.now(),
      });
    }
  },
});
