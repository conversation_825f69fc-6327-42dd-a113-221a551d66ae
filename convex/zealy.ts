"use node";

import { action, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Zealy API types
interface ZealyQuest {
  id: string;
  name: string;
  archived?: boolean;
}

interface ZealyUser {
  id: string;
  name: string;
}

interface ZealyTask {
  id: string;
  type: string;
  createdAt: string;
  tweetId?: string;
  value?: string;
}

interface ZealyReview {
  id: string;
  quest: {
    id: string;
    name: string;
  };
  user: ZealyUser;
  status: "pending" | "success" | "fail";
  createdAt: string;
  updatedAt: string;
  tasks: ZealyTask[];
}

interface ZealyQuestsResponse {
  items: ZealyQuest[];
}

interface ZealyReviewsResponse {
  items: ZealyReview[];
  nextCursor?: string;
}

// Helper to extract tweet ID from URL
function extractTweetIdFromUrl(url: string): string | null {
  const tweetUrlRegex = /(?:twitter\.com|x\.com)\/\w+\/status\/(\d+)/;
  const match = url.match(tweetUrlRegex);
  return match ? match[1] : null;
}

// Helper to make Zealy API calls with rate limiting
async function zealyApiCall(
  endpoint: string,
  apiKey: string,
  subdomain: string,
  params: Record<string, string> = {}
): Promise<any> {
  const url = new URL(`https://api.zealy.io/public/communities/${subdomain}${endpoint}`);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) url.searchParams.append(key, value);
  });

  const response = await fetch(url.toString(), {
    headers: {
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    if (response.status === 429) {
      // Rate limited - wait and retry
      await new Promise(resolve => setTimeout(resolve, 1000));
      return zealyApiCall(endpoint, apiKey, subdomain, params);
    }
    throw new Error(`Zealy API error: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

export const runZealyImport = action({
  args: {
    campaignId: v.id("campaigns"),
    fromDate: v.optional(v.number()),
    toDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Start import run
    const runId = await ctx.runMutation(internal.database.startImportRun, {
      campaignId: args.campaignId,
      runType: "zealy",
    });

    try {
      // Get campaign and credentials
      const campaign = await ctx.runQuery(internal.database.getCampaignWithCredentials, {
        campaignId: args.campaignId,
      });

      if (!campaign) {
        throw new Error("Campaign not found");
      }

      let questsScanned = 0;
      let reviewsScanned = 0;
      let tweetsExtracted = 0;

      // Step 1: Fetch and store quests
      console.log("Fetching quests...");
      const questsResponse: ZealyQuestsResponse = await zealyApiCall(
        "/quests",
        campaign.zealyApiKey,
        campaign.zealySubdomain
      );

      for (const quest of questsResponse.items) {
        await ctx.runMutation(internal.database.upsertQuest, {
          campaignId: args.campaignId,
          zealyQuestId: quest.id,
          name: quest.name,
          archived: quest.archived || false,
        });
        questsScanned++;
      }

      // Step 2: Fetch reviews for each quest
      console.log("Fetching reviews...");
      for (const quest of questsResponse.items) {
        let cursor: string | undefined;
        
        do {
          const params: Record<string, string> = {
            questId: quest.id,
            limit: "102",
          };
          if (cursor) params.cursor = cursor;

          const reviewsResponse: ZealyReviewsResponse = await zealyApiCall(
            "/reviews",
            campaign.zealyApiKey,
            campaign.zealySubdomain,
            params
          );

          for (const review of reviewsResponse.items) {
            // Filter by date if specified
            const reviewDate = new Date(review.createdAt).getTime();
            if (args.fromDate && reviewDate < args.fromDate) continue;
            if (args.toDate && reviewDate > args.toDate) continue;

            // Store review
            const reviewId = await ctx.runMutation(internal.database.upsertReview, {
              campaignId: args.campaignId,
              zealyReviewId: review.id,
              zealyUserId: review.user.id,
              zealyUserName: review.user.name,
              zealyQuestId: review.quest.id,
              zealyQuestName: review.quest.name,
              status: review.status,
              createdAt: new Date(review.createdAt).getTime(),
              updatedAt: new Date(review.updatedAt).getTime(),
            });

            reviewsScanned++;

            // Process tasks and extract tweets
            for (const task of review.tasks) {
              // Store task
              await ctx.runMutation(internal.database.upsertReviewTask, {
                reviewId,
                taskId: task.id,
                taskType: task.type,
                taskCreatedAt: new Date(task.createdAt).getTime(),
                rawValueJson: JSON.stringify(task),
              });

              // Extract tweet IDs
              const extractedTweets: Array<{
                tweetId: string;
                source: "tweet" | "tweetReact" | "url" | "text";
                reactTarget: boolean;
              }> = [];

              if (task.type === "tweet" && task.tweetId) {
                extractedTweets.push({
                  tweetId: task.tweetId,
                  source: "tweet",
                  reactTarget: false,
                });
              }

              if (task.type === "tweetReact" && task.tweetId) {
                extractedTweets.push({
                  tweetId: task.tweetId,
                  source: "tweetReact",
                  reactTarget: true,
                });
              }

              if ((task.type === "url" || task.type === "text") && task.value) {
                const tweetId = extractTweetIdFromUrl(task.value);
                if (tweetId) {
                  extractedTweets.push({
                    tweetId,
                    source: task.type as "url" | "text",
                    reactTarget: false,
                  });
                }
              }

              // Store extracted tweets
              for (const tweet of extractedTweets) {
                await ctx.runMutation(internal.database.upsertExtractedTweet, {
                  campaignId: args.campaignId,
                  reviewId,
                  taskId: task.id,
                  tweetId: tweet.tweetId,
                  tweetUrl: `https://x.com/i/status/${tweet.tweetId}`,
                  reactTarget: tweet.reactTarget,
                  source: tweet.source,
                });
                tweetsExtracted++;
              }
            }
          }

          cursor = reviewsResponse.nextCursor;
        } while (cursor);
      }

      // Complete import run
      await ctx.runMutation(internal.database.completeImportRun, {
        runId,
        questsScanned,
        reviewsScanned,
        tweetsExtracted,
        tweetsEnriched: 0,
      });

      return {
        success: true,
        questsScanned,
        reviewsScanned,
        tweetsExtracted,
      };

    } catch (error) {
      await ctx.runMutation(internal.database.failImportRun, {
        runId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  },
});





export const failImportRun = internalMutation({
  args: {
    runId: v.id("importRuns"),
    errorMessage: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.runId, {
      completedAt: Date.now(),
      status: "failed",
      errorMessage: args.errorMessage,
    });
  },
});

export const getCampaignWithCredentials = internalQuery({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign) return null;

    const zealyCredential = await ctx.db.get(campaign.zealyCredentialId);
    if (!zealyCredential) return null;

    // Decrypt API key (simplified - use proper decryption in production)
    const zealyApiKey = Buffer.from(zealyCredential.secretEnc, 'base64').toString();

    let twitterApiKey: string | undefined;
    if (campaign.twitterCredentialId) {
      const twitterCredential = await ctx.db.get(campaign.twitterCredentialId);
      if (twitterCredential) {
        twitterApiKey = Buffer.from(twitterCredential.secretEnc, 'base64').toString();
      }
    }

    return {
      ...campaign,
      zealyApiKey,
      twitterApiKey,
    };
  },
});

export const upsertQuest = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    zealyQuestId: v.string(),
    name: v.string(),
    archived: v.boolean(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("quests")
      .withIndex("by_zealy_quest_id", (q) => q.eq("zealyQuestId", args.zealyQuestId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        name: args.name,
        archived: args.archived,
        lastUpdated: Date.now(),
      });
      return existing._id;
    } else {
      return await ctx.db.insert("quests", {
        campaignId: args.campaignId,
        zealyQuestId: args.zealyQuestId,
        name: args.name,
        archived: args.archived,
        lastUpdated: Date.now(),
      });
    }
  },
});

export const upsertReview = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    zealyReviewId: v.string(),
    zealyUserId: v.string(),
    zealyUserName: v.string(),
    zealyQuestId: v.string(),
    zealyQuestName: v.string(),
    status: v.union(v.literal("pending"), v.literal("success"), v.literal("fail")),
    createdAt: v.number(),
    updatedAt: v.number(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("reviews")
      .withIndex("by_zealy_review_id", (q) => q.eq("zealyReviewId", args.zealyReviewId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        zealyUserName: args.zealyUserName,
        zealyQuestName: args.zealyQuestName,
        status: args.status,
        updatedAt: args.updatedAt,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("reviews", args);
    }
  },
});

export const upsertReviewTask = internalMutation({
  args: {
    reviewId: v.id("reviews"),
    taskId: v.string(),
    taskType: v.string(),
    taskCreatedAt: v.number(),
    rawValueJson: v.string(),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("reviewTasks")
      .withIndex("by_review", (q) => q.eq("reviewId", args.reviewId))
      .filter((q) => q.eq(q.field("taskId"), args.taskId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        taskType: args.taskType,
        taskCreatedAt: args.taskCreatedAt,
        rawValueJson: args.rawValueJson,
      });
      return existing._id;
    } else {
      return await ctx.db.insert("reviewTasks", args);
    }
  },
});

export const upsertExtractedTweet = internalMutation({
  args: {
    campaignId: v.id("campaigns"),
    reviewId: v.optional(v.id("reviews")),
    taskId: v.optional(v.string()),
    tweetId: v.string(),
    tweetUrl: v.string(),
    reactTarget: v.boolean(),
    source: v.union(
      v.literal("tweet"), 
      v.literal("tweetReact"), 
      v.literal("url"), 
      v.literal("text"),
      v.literal("keyword_search"),
      v.literal("ambassador_timeline")
    ),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("tweetsExtracted")
      .withIndex("by_tweet_and_review", (q) => 
        q.eq("tweetId", args.tweetId).eq("reviewId", args.reviewId ?? undefined)
      )
      .first();

    if (!existing) {
      await ctx.db.insert("tweetsExtracted", {
        ...args,
        extractedAt: Date.now(),
      });
    }
  },
});


