import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Simple encryption helper (in production, use proper key management)
function encrypt(text: string): string {
  // In a real app, use crypto.subtle or a proper encryption library
  // For now, just base64 encode (NOT SECURE - replace with AES-256-GCM)
  return Buffer.from(text).toString('base64');
}

function decrypt(encrypted: string): string {
  // In a real app, decrypt properly
  return Buffer.from(encrypted, 'base64').toString();
}

export const saveZealyCredential = mutation({
  args: {
    label: v.string(),
    apiKey: v.string(),
    subdomain: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const secretEnc = encrypt(args.apiKey);

    return await ctx.db.insert("credentials", {
      userId,
      kind: "zealy",
      label: args.label,
      secretEnc,
      extra: {
        subdomain: args.subdomain,
      },
    });
  },
});

export const saveTwitterCredential = mutation({
  args: {
    label: v.string(),
    apiKey: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const secretEnc = encrypt(args.apiKey);

    return await ctx.db.insert("credentials", {
      userId,
      kind: "twitterapi",
      label: args.label,
      secretEnc,
    });
  },
});

export const listCredentials = query({
  args: {
    kind: v.optional(v.union(v.literal("zealy"), v.literal("twitterapi"))),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    let credentials;
    
    if (args.kind) {
      credentials = await ctx.db
        .query("credentials")
        .withIndex("by_user_and_kind", (q) => q.eq("userId", userId).eq("kind", args.kind!))
        .collect();
    } else {
      credentials = await ctx.db
        .query("credentials")
        .filter((q) => q.eq(q.field("userId"), userId))
        .collect();
    }

    // Don't return the encrypted secret
    return credentials.map(({ secretEnc, ...cred }) => cred);
  },
});

export const getDecryptedCredential = query({
  args: {
    credentialId: v.id("credentials"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const credential = await ctx.db.get(args.credentialId);
    if (!credential || credential.userId !== userId) {
      throw new Error("Credential not found");
    }

    return {
      ...credential,
      apiKey: decrypt(credential.secretEnc),
    };
  },
});
