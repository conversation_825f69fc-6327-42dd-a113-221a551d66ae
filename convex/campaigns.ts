import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const createCampaign = mutation({
  args: {
    name: v.string(),
    zealySubdomain: v.string(),
    zealyCredentialId: v.id("credentials"),
    twitterCredentialId: v.optional(v.id("credentials")),
    keywordSets: v.optional(v.array(v.string())),
    ambassadorUsernames: v.optional(v.array(v.string())),
    useTwitterCreatedAt: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify the Zealy credential belongs to the user
    const zealyCredential = await ctx.db.get(args.zealyCredentialId);
    if (!zealyCredential || zealyCredential.userId !== userId || zealyCredential.kind !== "zealy") {
      throw new Error("Invalid Zealy credential");
    }

    // Verify the Twitter credential if provided
    if (args.twitterCredentialId) {
      const twitterCredential = await ctx.db.get(args.twitterCredentialId);
      if (!twitterCredential || twitterCredential.userId !== userId || twitterCredential.kind !== "twitterapi") {
        throw new Error("Invalid Twitter credential");
      }
    }

    return await ctx.db.insert("campaigns", {
      userId,
      name: args.name,
      zealySubdomain: args.zealySubdomain,
      zealyCredentialId: args.zealyCredentialId,
      twitterCredentialId: args.twitterCredentialId,
      keywordSets: args.keywordSets || [],
      ambassadorUsernames: args.ambassadorUsernames || [],
      useTwitterCreatedAt: args.useTwitterCreatedAt || false,
      isActive: true,
    });
  },
});

export const updateCampaign = mutation({
  args: {
    campaignId: v.id("campaigns"),
    name: v.optional(v.string()),
    twitterCredentialId: v.optional(v.id("credentials")),
    keywordSets: v.optional(v.array(v.string())),
    ambassadorUsernames: v.optional(v.array(v.string())),
    useTwitterCreatedAt: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign || campaign.userId !== userId) {
      throw new Error("Campaign not found");
    }

    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name;
    if (args.twitterCredentialId !== undefined) updates.twitterCredentialId = args.twitterCredentialId;
    if (args.keywordSets !== undefined) updates.keywordSets = args.keywordSets;
    if (args.ambassadorUsernames !== undefined) updates.ambassadorUsernames = args.ambassadorUsernames;
    if (args.useTwitterCreatedAt !== undefined) updates.useTwitterCreatedAt = args.useTwitterCreatedAt;

    await ctx.db.patch(args.campaignId, updates);
  },
});

export const listCampaigns = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("campaigns")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
  },
});

export const getCampaign = query({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign || campaign.userId !== userId) {
      throw new Error("Campaign not found");
    }

    return campaign;
  },
});
