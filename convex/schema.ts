import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  // Encrypted API credentials
  credentials: defineTable({
    userId: v.id("users"),
    kind: v.union(v.literal("zealy"), v.literal("twitterapi")),
    label: v.string(),
    secretEnc: v.string(), // AES-256-GCM encrypted
    extra: v.optional(v.object({
      subdomain: v.optional(v.string()), // For Zealy
    })),
  }).index("by_user_and_kind", ["userId", "kind"]),

  // Campaigns (one per Zealy community)
  campaigns: defineTable({
    userId: v.id("users"),
    name: v.string(),
    zealySubdomain: v.string(),
    zealyCredentialId: v.id("credentials"),
    twitterCredentialId: v.optional(v.id("credentials")),
    lastRunAt: v.optional(v.number()),
    isActive: v.boolean(),
    // Twitter integration settings
    keywordSets: v.optional(v.array(v.string())),
    ambassadorUsernames: v.optional(v.array(v.string())),
    useTwitterCreatedAt: v.optional(v.boolean()),
  }).index("by_user", ["userId"]),

  // Zealy quests
  quests: defineTable({
    campaignId: v.id("campaigns"),
    zealyQuestId: v.string(),
    name: v.string(),
    archived: v.boolean(),
    lastUpdated: v.number(),
  })
    .index("by_campaign", ["campaignId"])
    .index("by_zealy_quest_id", ["zealyQuestId"]),

  // Zealy reviews (claimed quests)
  reviews: defineTable({
    campaignId: v.id("campaigns"),
    zealyReviewId: v.string(),
    zealyUserId: v.string(),
    zealyUserName: v.string(),
    zealyQuestId: v.string(),
    zealyQuestName: v.string(),
    status: v.union(v.literal("pending"), v.literal("success"), v.literal("fail")),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_campaign", ["campaignId"])
    .index("by_zealy_review_id", ["zealyReviewId"])
    .index("by_campaign_and_created", ["campaignId", "createdAt"]),

  // Individual tasks within reviews
  reviewTasks: defineTable({
    reviewId: v.id("reviews"),
    taskId: v.string(),
    taskType: v.string(),
    taskCreatedAt: v.number(),
    rawValueJson: v.string(), // JSON string of the raw task data
  }).index("by_review", ["reviewId"]),

  // Extracted tweet IDs from tasks and Twitter API searches
  tweetsExtracted: defineTable({
    campaignId: v.id("campaigns"),
    reviewId: v.optional(v.id("reviews")), // null for Twitter API discovered tweets
    taskId: v.optional(v.string()),
    tweetId: v.string(),
    tweetUrl: v.string(),
    reactTarget: v.boolean(), // true if from tweetReact task
    source: v.union(
      v.literal("tweet"),
      v.literal("tweetReact"), 
      v.literal("url"),
      v.literal("text"),
      v.literal("keyword_search"),
      v.literal("ambassador_timeline")
    ),
    extractedAt: v.number(),
    // Twitter API enrichment data
    authorUsername: v.optional(v.string()),
    authorName: v.optional(v.string()),
    tweetText: v.optional(v.string()),
    tweetCreatedAt: v.optional(v.number()),
    likeCount: v.optional(v.number()),
    replyCount: v.optional(v.number()),
    retweetCount: v.optional(v.number()),
    quoteCount: v.optional(v.number()),
    viewCount: v.optional(v.number()),
    enrichedAt: v.optional(v.number()),
  })
    .index("by_campaign", ["campaignId"])
    .index("by_tweet_and_review", ["tweetId", "reviewId"])
    .index("by_tweet_id", ["tweetId"]),

  // Import runs for tracking
  importRuns: defineTable({
    campaignId: v.id("campaigns"),
    userId: v.id("users"),
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    status: v.union(v.literal("running"), v.literal("completed"), v.literal("failed")),
    runType: v.union(v.literal("zealy"), v.literal("twitter_keywords"), v.literal("twitter_ambassadors"), v.literal("twitter_enrichment")),
    questsScanned: v.number(),
    reviewsScanned: v.number(),
    tweetsExtracted: v.number(),
    tweetsEnriched: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
  }).index("by_campaign", ["campaignId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
