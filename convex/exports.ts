import { query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Helper to get start of ISO week (Monday)
function getISOWeekStart(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  return new Date(d.setDate(diff));
}

// Helper to get end of ISO week (Sunday)
function getISOWeekEnd(date: Date): Date {
  const start = getISOWeekStart(date);
  const end = new Date(start);
  end.setDate(start.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  return end;
}

export const getExportData = query({
  args: {
    campaignId: v.id("campaigns"),
    fromDate: v.number(),
    toDate: v.number(),
    bucketType: v.union(v.literal("weekly"), v.literal("custom")),
    customBuckets: v.optional(v.array(v.object({
      start: v.number(),
      end: v.number(),
      label: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify campaign ownership
    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign || campaign.userId !== userId) {
      throw new Error("Campaign not found");
    }

    // Get all extracted tweets in date range
    const tweets = await ctx.db
      .query("tweetsExtracted")
      .withIndex("by_campaign", (q) => q.eq("campaignId", args.campaignId))
      .collect();

    // Get associated reviews for Zealy-sourced tweets
    const reviewIds = [...new Set(tweets.map(t => t.reviewId).filter(Boolean))];
    const reviews = await Promise.all(
      reviewIds.map(id => ctx.db.get(id!))
    );
    const reviewsMap = new Map(reviews.filter(r => r).map(r => [r!._id, r!]));

    // Filter tweets by date range
    const filteredTweets = tweets.filter(tweet => {
      let tweetDate: number;
      
      // Use Twitter createdAt if available and campaign is configured to use it
      if (campaign.useTwitterCreatedAt && tweet.tweetCreatedAt) {
        tweetDate = tweet.tweetCreatedAt;
      } else if (tweet.reviewId) {
        // Use review createdAt for Zealy-sourced tweets
        const review = reviewsMap.get(tweet.reviewId);
        if (!review) return false;
        tweetDate = review.createdAt;
      } else {
        // Use extractedAt for Twitter API sourced tweets
        tweetDate = tweet.extractedAt;
      }
      
      return tweetDate >= args.fromDate && tweetDate <= args.toDate;
    });

    // Generate buckets
    let buckets: Array<{ start: number; end: number; label: string }> = [];
    
    if (args.bucketType === "weekly") {
      const startDate = new Date(args.fromDate);
      const endDate = new Date(args.toDate);
      
      let currentWeekStart = getISOWeekStart(startDate);
      
      while (currentWeekStart <= endDate) {
        const weekEnd = getISOWeekEnd(currentWeekStart);
        buckets.push({
          start: currentWeekStart.getTime(),
          end: weekEnd.getTime(),
          label: `Week of ${currentWeekStart.toISOString().split('T')[0]}`,
        });
        
        currentWeekStart = new Date(currentWeekStart);
        currentWeekStart.setDate(currentWeekStart.getDate() + 7);
      }
    } else if (args.customBuckets) {
      buckets = args.customBuckets;
    }

    // Group tweets by bucket
    const exportRows: Array<{
      client: string;
      bucket_start: string;
      bucket_end: string;
      quest_id: string;
      quest_name: string;
      review_id: string;
      review_status: string;
      zealy_user_id: string;
      zealy_user_name: string;
      task_type: string;
      tweet_id: string;
      tweet_url: string;
      review_created_at: string;
      review_updated_at: string;
      task_created_at: string;
      react_target: boolean;
      source: string;
      // Twitter enrichment fields
      author_username: string;
      author_name: string;
      tweet_text: string;
      tweet_created_at: string;
      like_count: number;
      reply_count: number;
      retweet_count: number;
      quote_count: number;
      view_count: number;
    }> = [];

    for (const bucket of buckets) {
      const bucketTweets = filteredTweets.filter(tweet => {
        let tweetDate: number;
        
        if (campaign.useTwitterCreatedAt && tweet.tweetCreatedAt) {
          tweetDate = tweet.tweetCreatedAt;
        } else if (tweet.reviewId) {
          const review = reviewsMap.get(tweet.reviewId);
          if (!review) return false;
          tweetDate = review.createdAt;
        } else {
          tweetDate = tweet.extractedAt;
        }
        
        return tweetDate >= bucket.start && tweetDate <= bucket.end;
      });

      for (const tweet of bucketTweets) {
        const review = tweet.reviewId ? reviewsMap.get(tweet.reviewId) : null;

        // Get task details if available
        let task = null;
        if (tweet.reviewId && tweet.taskId) {
          task = await ctx.db
            .query("reviewTasks")
            .withIndex("by_review", (q) => q.eq("reviewId", tweet.reviewId!))
            .filter((q) => q.eq(q.field("taskId"), tweet.taskId || ""))
            .first();
        }

        exportRows.push({
          client: campaign.name,
          bucket_start: new Date(bucket.start).toISOString(),
          bucket_end: new Date(bucket.end).toISOString(),
          quest_id: review?.zealyQuestId || "",
          quest_name: review?.zealyQuestName || "",
          review_id: review?.zealyReviewId || "",
          review_status: review?.status || "",
          zealy_user_id: review?.zealyUserId || "",
          zealy_user_name: review?.zealyUserName || "",
          task_type: task?.taskType || "",
          tweet_id: tweet.tweetId,
          tweet_url: tweet.tweetUrl,
          review_created_at: review ? new Date(review.createdAt).toISOString() : "",
          review_updated_at: review ? new Date(review.updatedAt).toISOString() : "",
          task_created_at: task ? new Date(task.taskCreatedAt).toISOString() : "",
          react_target: tweet.reactTarget,
          source: tweet.source,
          // Twitter enrichment fields
          author_username: tweet.authorUsername || "",
          author_name: tweet.authorName || "",
          tweet_text: tweet.tweetText || "",
          tweet_created_at: tweet.tweetCreatedAt ? new Date(tweet.tweetCreatedAt).toISOString() : "",
          like_count: tweet.likeCount || 0,
          reply_count: tweet.replyCount || 0,
          retweet_count: tweet.retweetCount || 0,
          quote_count: tweet.quoteCount || 0,
          view_count: tweet.viewCount || 0,
        });
      }
    }

    return {
      rows: exportRows,
      summary: {
        totalTweets: exportRows.length,
        uniqueTweetIds: new Set(exportRows.map(r => r.tweet_id)).size,
        buckets: buckets.length,
        dateRange: {
          from: new Date(args.fromDate).toISOString(),
          to: new Date(args.toDate).toISOString(),
        },
        enrichedTweets: exportRows.filter(r => r.author_username).length,
      },
    };
  },
});

export const getImportRuns = query({
  args: {
    campaignId: v.id("campaigns"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    // Verify campaign ownership
    const campaign = await ctx.db.get(args.campaignId);
    if (!campaign || campaign.userId !== userId) {
      return [];
    }

    return await ctx.db
      .query("importRuns")
      .withIndex("by_campaign", (q) => q.eq("campaignId", args.campaignId))
      .order("desc")
      .take(20);
  },
});
